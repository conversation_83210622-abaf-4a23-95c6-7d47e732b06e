'use client'
import React from 'react'
import { motion } from 'framer-motion'
import { Star, Award, Users, BookOpen, Linkedin, Twitter, Github } from 'lucide-react'

const Instructors = () => {
  const instructors = [
    {
      id: 1,
      name: 'Dr. <PERSON>',
      title: 'Senior Software Engineer at Google',
      specialization: 'React & Frontend Development',
      image: '👩‍💻',
      rating: 4.9,
      students: 25000,
      courses: 12,
      experience: '8+ years',
      bio: 'Former Google engineer with expertise in modern web development and React ecosystem.',
      social: {
        linkedin: '#',
        twitter: '#',
        github: '#'
      }
    },
    {
      id: 2,
      name: 'Prof. <PERSON>',
      title: 'Data Science Lead at Microsoft',
      specialization: 'Python & Machine Learning',
      image: '👨‍🔬',
      rating: 4.8,
      students: 18500,
      courses: 8,
      experience: '10+ years',
      bio: 'PhD in Computer Science, specializing in AI and machine learning applications.',
      social: {
        linkedin: '#',
        twitter: '#',
        github: '#'
      }
    },
    {
      id: 3,
      name: '<PERSON>',
      title: 'Creative Director at Adobe',
      specialization: 'UI/UX Design',
      image: '👩‍🎨',
      rating: 4.9,
      students: 22000,
      courses: 15,
      experience: '7+ years',
      bio: 'Award-winning designer with a passion for creating beautiful and functional user experiences.',
      social: {
        linkedin: '#',
        twitter: '#',
        github: '#'
      }
    },
    {
      id: 4,
      name: '<PERSON> Park',
      title: 'Mobile App Architect',
      specialization: 'Flutter & Mobile Development',
      image: '👨‍💼',
      rating: 4.7,
      students: 15000,
      courses: 10,
      experience: '6+ years',
      bio: 'Mobile development expert with apps downloaded by millions of users worldwide.',
      social: {
        linkedin: '#',
        twitter: '#',
        github: '#'
      }
    }
  ]

  return (
    <section id="instructors" className="py-20 bg-gradient-to-br from-blue-50 to-purple-50">
      <div className="container mx-auto px-4">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          className="text-center mb-16"
        >
          <h2 className="text-5xl font-bold mb-6">
            Meet Our <span className="bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">Expert</span> Instructors
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Learn from industry leaders and experienced professionals who are passionate about sharing their knowledge and helping you succeed.
          </p>
        </motion.div>

        {/* Instructors Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
          {instructors.map((instructor, index) => (
            <motion.div
              key={instructor.id}
              initial={{ opacity: 0, y: 50 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              whileHover={{ y: -10 }}
              className="bg-white rounded-2xl overflow-hidden shadow-lg hover:shadow-2xl transition-all duration-300 group"
            >
              {/* Profile Image */}
              <div className="relative h-48 bg-gradient-to-br from-blue-100 to-purple-100 flex items-center justify-center">
                <div className="text-6xl group-hover:scale-110 transition-transform duration-300">
                  {instructor.image}
                </div>
                <div className="absolute top-4 right-4 bg-white/90 backdrop-blur-sm px-2 py-1 rounded-full flex items-center space-x-1">
                  <Star className="w-4 h-4 text-yellow-400 fill-current" />
                  <span className="text-sm font-medium">{instructor.rating}</span>
                </div>
              </div>

              {/* Content */}
              <div className="p-6">
                <h3 className="text-xl font-bold text-gray-900 mb-1 group-hover:text-blue-600 transition-colors">
                  {instructor.name}
                </h3>
                <p className="text-blue-600 font-medium text-sm mb-2">{instructor.title}</p>
                <p className="text-gray-600 text-sm mb-4">{instructor.specialization}</p>
                
                <p className="text-gray-600 text-sm mb-4 line-clamp-2">{instructor.bio}</p>

                {/* Stats */}
                <div className="grid grid-cols-3 gap-2 mb-4 text-center">
                  <div>
                    <div className="flex items-center justify-center mb-1">
                      <Users className="w-4 h-4 text-blue-600" />
                    </div>
                    <div className="text-sm font-bold text-gray-900">{instructor.students.toLocaleString()}</div>
                    <div className="text-xs text-gray-500">Students</div>
                  </div>
                  <div>
                    <div className="flex items-center justify-center mb-1">
                      <BookOpen className="w-4 h-4 text-purple-600" />
                    </div>
                    <div className="text-sm font-bold text-gray-900">{instructor.courses}</div>
                    <div className="text-xs text-gray-500">Courses</div>
                  </div>
                  <div>
                    <div className="flex items-center justify-center mb-1">
                      <Award className="w-4 h-4 text-orange-500" />
                    </div>
                    <div className="text-sm font-bold text-gray-900">{instructor.experience}</div>
                    <div className="text-xs text-gray-500">Experience</div>
                  </div>
                </div>

                {/* Social Links */}
                <div className="flex justify-center space-x-3 pt-4 border-t border-gray-100">
                  {[
                    { icon: Linkedin, href: instructor.social.linkedin, color: 'text-blue-600' },
                    { icon: Twitter, href: instructor.social.twitter, color: 'text-sky-500' },
                    { icon: Github, href: instructor.social.github, color: 'text-gray-700' }
                  ].map((social, socialIndex) => (
                    <motion.a
                      key={socialIndex}
                      href={social.href}
                      whileHover={{ scale: 1.2 }}
                      whileTap={{ scale: 0.9 }}
                      className={`${social.color} hover:opacity-70 transition-opacity`}
                    >
                      <social.icon className="w-5 h-5" />
                    </motion.a>
                  ))}
                </div>
              </div>
            </motion.div>
          ))}
        </div>

        {/* CTA Section */}
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.4 }}
          className="text-center mt-16"
        >
          <div className="bg-white rounded-2xl p-8 shadow-lg max-w-2xl mx-auto">
            <h3 className="text-2xl font-bold text-gray-900 mb-4">
              Want to Become an Instructor?
            </h3>
            <p className="text-gray-600 mb-6">
              Join our community of expert instructors and share your knowledge with thousands of eager learners worldwide.
            </p>
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="bg-gradient-to-r from-blue-600 to-purple-600 text-white px-8 py-3 rounded-full font-medium hover:shadow-lg transition-all duration-200"
            >
              Apply to Teach
            </motion.button>
          </div>
        </motion.div>
      </div>
    </section>
  )
}

export default Instructors
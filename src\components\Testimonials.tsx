'use client'
import React, { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Star, Quote, ChevronLeft, ChevronRight } from 'lucide-react'

const Testimonials = () => {
  const [currentIndex, setCurrentIndex] = useState(0)

  const testimonials = [
    {
      id: 1,
      name: '<PERSON>',
      role: 'Software Developer at Netflix',
      image: '👨‍💻',
      rating: 5,
      text: 'EduTech completely transformed my career. The React course was incredibly comprehensive and the instructors were always available to help. I landed my dream job at Netflix just 3 months after completing the program!',
      course: 'Complete React Development',
      location: 'San Francisco, CA'
    },
    {
      id: 2,
      name: '<PERSON>',
      role: 'UX Designer at Spotify',
      image: '👩‍🎨',
      rating: 5,
      text: 'The UI/UX Design Masterclass exceeded all my expectations. The hands-on projects and real-world case studies prepared me perfectly for my role at Spotify. The community support was amazing too!',
      course: 'UI/UX Design Masterclass',
      location: 'Barcelona, Spain'
    },
    {
      id: 3,
      name: '<PERSON>',
      role: 'Data Scientist at Tesla',
      image: '👨‍🔬',
      rating: 5,
      text: 'As someone transitioning from finance to tech, EduTech made the journey smooth and enjoyable. The Python for Data Science course was perfectly structured, and I now work as a Data Scientist at Tesla!',
      course: 'Python for Data Science',
      location: 'Austin, TX'
    },
    {
      id: 4,
      name: 'Sophie Chen',
      role: 'Mobile Developer at Uber',
      image: '👩‍💼',
      rating: 5,
      text: 'The Flutter course was exactly what I needed to advance my mobile development skills. The instructor was knowledgeable and the projects were challenging yet achievable. Highly recommend!',
      course: 'Flutter Mobile Development',
      location: 'Toronto, Canada'
    },
    {
      id: 5,
      name: 'David Kumar',
      role: 'Full Stack Developer at Airbnb',
      image: '👨‍💻',
      rating: 5,
      text: 'EduTech\'s comprehensive curriculum and expert instructors helped me transition from a non-tech background to becoming a full-stack developer at Airbnb. The support throughout the journey was incredible!',
      course: 'Full Stack Development',
      location: 'London, UK'
    }
  ]

  const nextTestimonial = () => {
    setCurrentIndex((prev) => (prev + 1) % testimonials.length)
  }

  const prevTestimonial = () => {
    setCurrentIndex((prev) => (prev - 1 + testimonials.length) % testimonials.length)
  }

  useEffect(() => {
    const interval = setInterval(nextTestimonial, 5000)
    return () => clearInterval(interval)
  }, [])

  return (
    <section id="testimonials" className="py-20 bg-white">
      <div className="container mx-auto px-4">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          className="text-center mb-16"
        >
          <h2 className="text-5xl font-bold mb-6">
            What Our <span className="bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">Students</span> Say
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Don't just take our word for it. Here's what our successful graduates have to say about their learning journey with EduTech.
          </p>
        </motion.div>

        {/* Main Testimonial */}
        <div className="relative max-w-4xl mx-auto">
          <AnimatePresence mode="wait">
            <motion.div
              key={currentIndex}
              initial={{ opacity: 0, x: 100 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -100 }}
              transition={{ duration: 0.5 }}
              className="bg-gradient-to-br from-blue-50 to-purple-50 rounded-3xl p-8 md:p-12 shadow-lg border border-blue-100"
            >
              <div className="flex flex-col md:flex-row items-center gap-8">
                {/* Profile */}
                <div className="flex-shrink-0 text-center">
                  <div className="w-24 h-24 bg-gradient-to-br from-blue-100 to-purple-100 rounded-full flex items-center justify-center text-4xl mb-4 mx-auto">
                    {testimonials[currentIndex].image}
                  </div>
                  <h3 className="text-xl font-bold text-gray-900">{testimonials[currentIndex].name}</h3>
                  <p className="text-blue-600 font-medium">{testimonials[currentIndex].role}</p>
                  <p className="text-gray-500 text-sm">{testimonials[currentIndex].location}</p>
                </div>

                {/* Content */}
                <div className="flex-1">
                  <Quote className="w-8 h-8 text-blue-600 mb-4" />
                  <p className="text-lg text-gray-700 leading-relaxed mb-6">
                    "{testimonials[currentIndex].text}"
                  </p>
                  
                  <div className="flex items-center justify-between">
                    <div>
                      <div className="flex items-center space-x-1 mb-2">
                        {[...Array(testimonials[currentIndex].rating)].map((_, i) => (
                          <Star key={i} className="w-5 h-5 text-yellow-400 fill-current" />
                        ))}
                      </div>
                      <p className="text-sm text-gray-600">
                        Course: <span className="font-medium">{testimonials[currentIndex].course}</span>
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </motion.div>
          </AnimatePresence>

          {/* Navigation Buttons */}
          <div className="flex justify-center space-x-4 mt-8">
            <motion.button
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
              onClick={prevTestimonial}
              className="w-12 h-12 bg-white rounded-full shadow-lg flex items-center justify-center text-gray-600 hover:text-blue-600 transition-colors border border-gray-200"
            >
              <ChevronLeft className="w-6 h-6" />
            </motion.button>
            <motion.button
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
              onClick={nextTestimonial}
              className="w-12 h-12 bg-white rounded-full shadow-lg flex items-center justify-center text-gray-600 hover:text-blue-600 transition-colors border border-gray-200"
            >
              <ChevronRight className="w-6 h-6" />
            </motion.button>
          </div>

          {/* Dots Indicator */}
          <div className="flex justify-center space-x-2 mt-6">
            {testimonials.map((_, index) => (
              <button
                key={index}
                onClick={() => setCurrentIndex(index)}
                className={`w-3 h-3 rounded-full transition-all duration-300 ${
                  index === currentIndex 
                    ? 'bg-blue-600 w-8' 
                    : 'bg-gray-300 hover:bg-gray-400'
                }`}
              />
            ))}
          </div>
        </div>

        {/* Additional Testimonials Grid */}
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.4 }}
          className="grid md:grid-cols-3 gap-6 mt-16"
        >
          {testimonials.slice(0, 3).map((testimonial, index) => (
            <motion.div
              key={testimonial.id}
              whileHover={{ y: -5 }}
              className="bg-white rounded-xl p-6 shadow-lg border border-gray-100 hover:shadow-xl transition-all duration-300"
            >
              <div className="flex items-center space-x-1 mb-3">
                {[...Array(testimonial.rating)].map((_, i) => (
                  <Star key={i} className="w-4 h-4 text-yellow-400 fill-current" />
                ))}
              </div>
              <p className="text-gray-700 mb-4 text-sm line-clamp-3">
                "{testimonial.text}"
              </p>
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-gradient-to-br from-blue-100 to-purple-100 rounded-full flex items-center justify-center text-lg">
                  {testimonial.image}
                </div>
                <div>
                  <h4 className="font-medium text-gray-900 text-sm">{testimonial.name}</h4>
                  <p className="text-gray-500 text-xs">{testimonial.role}</p>
                </div>
              </div>
            </motion.div>
          ))}
        </motion.div>
      </div>
    </section>
  )
}

export default Testimonials
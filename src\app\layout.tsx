import type { Metada<PERSON>, Viewport } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mon<PERSON> } from "next/font/google";
import "./globals.css";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
  display: 'swap',
  preload: true,
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
  display: 'swap',
});

export const viewport: Viewport = {
  width: 'device-width',
  initialScale: 1,
  maximumScale: 5,
  userScalable: true,
  themeColor: [
    { media: '(prefers-color-scheme: light)', color: '#ffffff' },
    { media: '(prefers-color-scheme: dark)', color: '#1e40af' }
  ],
}

export const metadata: Metadata = {
  title: {
    default: "EduTech - Modern Learning Platform",
    template: "%s | EduTech"
  },
  description: "Transform your learning journey with our cutting-edge educational platform. Interactive courses, expert instructors, and personalized learning paths.",
  keywords: ["online education", "e-learning", "courses", "certification", "skill development", "programming", "technology", "learning platform"],
  authors: [{ name: "EduTech Team" }],
  creator: "EduTech Learning Platform",
  publisher: "EduTech",
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  metadataBase: new URL('https://edutech.com'),
  alternates: {
    canonical: '/',
  },
  openGraph: {
    type: 'website',
    locale: 'en_US',
    url: 'https://edutech.com',
    title: 'EduTech - Modern Learning Platform',
    description: 'Transform your learning journey with our cutting-edge educational platform.',
    siteName: 'EduTech',
    images: [
      {
        url: '/og-image.jpg',
        width: 1200,
        height: 630,
        alt: 'EduTech - Modern Learning Platform',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'EduTech - Modern Learning Platform',
    description: 'Transform your learning journey with our cutting-edge educational platform.',
    images: ['/og-image.jpg'],
    creator: '@edutech',
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" className="scroll-smooth">
      <head>
        <link rel="icon" href="/favicon.ico" sizes="any" />
        <link rel="icon" href="/icon.svg" type="image/svg+xml" />
        <link rel="apple-touch-icon" href="/apple-touch-icon.png" />
        <link rel="manifest" href="/manifest.json" />
      </head>
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased bg-white text-gray-900 overflow-x-hidden`}
        style={{ background: '#fff' }}
      >
        {children}
      </body>
    </html>
  );
}



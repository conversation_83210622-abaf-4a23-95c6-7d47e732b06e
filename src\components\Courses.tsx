'use client'
import React, { useState } from 'react'
import { motion } from 'framer-motion'
import { Clock, Users, Star, BookOpen, <PERSON>, Palette, <PERSON><PERSON><PERSON>, Smartphone } from 'lucide-react'

const Courses = () => {
  const [activeCategory, setActiveCategory] = useState('all')

  const categories = [
    { id: 'all', label: 'All Courses', icon: BookOpen },
    { id: 'programming', label: 'Programming', icon: Code },
    { id: 'design', label: 'Design', icon: Palette },
    { id: 'business', label: 'Business', icon: <PERSON><PERSON><PERSON> },
    { id: 'mobile', label: 'Mobile Dev', icon: Smartphone },
  ]

  const courses = [
    {
      id: 1,
      title: 'Complete React Development',
      category: 'programming',
      instructor: '<PERSON>',
      rating: 4.9,
      students: 12500,
      duration: '40 hours',
      price: 99,
      image: '⚛️',
      level: 'Intermediate',
      description: 'Master React from basics to advanced concepts'
    },
    {
      id: 2,
      title: 'UI/UX Design Masterclass',
      category: 'design',
      instructor: '<PERSON>',
      rating: 4.8,
      students: 8900,
      duration: '35 hours',
      price: 89,
      image: '🎨',
      level: 'Beginner',
      description: 'Learn modern UI/UX design principles'
    },
    {
      id: 3,
      title: 'Python for Data Science',
      category: 'programming',
      instructor: '<PERSON>',
      rating: 4.9,
      students: 15600,
      duration: '50 hours',
      price: 129,
      image: '🐍',
      level: 'Advanced',
      description: 'Data analysis and machine learning with Python'
    },
    {
      id: 4,
      title: 'Digital Marketing Strategy',
      category: 'business',
      instructor: 'Emma Wilson',
      rating: 4.7,
      students: 6700,
      duration: '25 hours',
      price: 79,
      image: '📈',
      level: 'Beginner',
      description: 'Complete digital marketing from scratch'
    },
    {
      id: 5,
      title: 'Flutter Mobile Development',
      category: 'mobile',
      instructor: 'David Park',
      rating: 4.8,
      students: 9200,
      duration: '45 hours',
      price: 119,
      image: '📱',
      level: 'Intermediate',
      description: 'Build cross-platform mobile apps'
    },
    {
      id: 6,
      title: 'Advanced JavaScript',
      category: 'programming',
      instructor: 'Alex Rodriguez',
      rating: 4.9,
      students: 11300,
      duration: '38 hours',
      price: 109,
      image: '🟨',
      level: 'Advanced',
      description: 'Deep dive into modern JavaScript'
    }
  ]

  const filteredCourses = activeCategory === 'all' 
    ? courses 
    : courses.filter(course => course.category === activeCategory)

  return (
    <section id="courses" className="section-full section-white py-20">
      <div className="container mx-auto">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          className="text-center mb-16"
        >
          <h2 className="text-5xl font-bold mb-6 text-black">
            Popular <span className="text-orange-500">Courses</span>
          </h2>
          <p className="text-xl text-gray-700 max-w-3xl mx-auto">
            Choose from our extensive library of courses designed by industry experts to help you achieve your learning goals.
          </p>
        </motion.div>

        {/* Category Filter */}
        <motion.div 
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
          className="flex flex-wrap justify-center gap-4 mb-12"
        >
          {categories.map((category, index) => (
            <motion.button
              key={category.id}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              onClick={() => setActiveCategory(category.id)}
              className={`flex items-center space-x-2 px-6 py-3 rounded-full transition-all duration-300 ${
                activeCategory === category.id
                  ? 'bg-orange-500 text-white shadow-lg'
                  : 'bg-white text-gray-600 hover:bg-gray-100 border border-gray-200'
              }`}
            >
              <category.icon className="w-5 h-5" />
              <span className="font-medium">{category.label}</span>
            </motion.button>
          ))}
        </motion.div>

        {/* Courses Grid */}
        <motion.div 
          layout
          className="grid md:grid-cols-2 lg:grid-cols-3 gap-8"
        >
          {filteredCourses.map((course, index) => (
            <motion.div
              key={course.id}
              layout
              initial={{ opacity: 0, y: 50 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              whileHover={{ y: -10 }}
              className="bg-white rounded-2xl overflow-hidden shadow-lg hover:shadow-2xl transition-all duration-300 group"
            >
              {/* Course Image */}
              <div className="relative h-48 bg-gradient-to-br from-blue-100 to-purple-100 flex items-center justify-center">
                <div className="text-6xl group-hover:scale-110 transition-transform duration-300">
                  {course.image}
                </div>
                <div className="absolute top-4 right-4 bg-white/90 backdrop-blur-sm px-3 py-1 rounded-full text-sm font-medium text-gray-700">
                  {course.level}
                </div>
              </div>

              {/* Course Content */}
              <div className="p-6">
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center space-x-1">
                    <Star className="w-4 h-4 text-yellow-400 fill-current" />
                    <span className="text-sm font-medium text-gray-700">{course.rating}</span>
                    <span className="text-sm text-gray-500">({course.students.toLocaleString()})</span>
                  </div>
                  <div className="text-2xl font-bold text-blue-600">${course.price}</div>
                </div>

                <h3 className="text-xl font-bold text-gray-900 mb-2 group-hover:text-blue-600 transition-colors">
                  {course.title}
                </h3>
                
                <p className="text-gray-600 mb-4 text-sm">
                  {course.description}
                </p>

                <div className="flex items-center justify-between text-sm text-gray-500 mb-4">
                  <div className="flex items-center space-x-1">
                    <Clock className="w-4 h-4" />
                    <span>{course.duration}</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <Users className="w-4 h-4" />
                    <span>{course.students.toLocaleString()} students</span>
                  </div>
                </div>

                <div className="flex items-center justify-between">
                  <div className="text-sm text-gray-600">
                    by <span className="font-medium">{course.instructor}</span>
                  </div>
                  <motion.button
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    className="bg-gradient-to-r from-blue-600 to-purple-600 text-white px-4 py-2 rounded-lg text-sm font-medium hover:shadow-lg transition-all duration-200"
                  >
                    Enroll Now
                  </motion.button>
                </div>
              </div>
            </motion.div>
          ))}
        </motion.div>

        {/* View All Button */}
        <motion.div 
          initial={{ opacity: 0 }}
          whileInView={{ opacity: 1 }}
          transition={{ duration: 0.6, delay: 0.4 }}
          className="text-center mt-12"
        >
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            className="bg-gradient-to-r from-blue-600 to-purple-600 text-white px-8 py-4 rounded-full text-lg font-medium hover:shadow-xl transition-all duration-300"
          >
            View All Courses
          </motion.button>
        </motion.div>
      </div>
    </section>
  )
}

export default Courses
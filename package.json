{"name": "lerna-agency", "version": "1.0.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "analyze": "cross-env ANALYZE=true next build", "type-check": "tsc --noEmit"}, "dependencies": {"react": "19.1.0", "react-dom": "19.1.0", "next": "15.4.1", "framer-motion": "^11.0.0", "lucide-react": "^0.400.0", "react-intersection-observer": "^9.5.0", "react-countup": "^6.5.0"}, "devDependencies": {"typescript": "^5", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@tailwindcss/postcss": "^4", "tailwindcss": "^4", "eslint": "^9", "eslint-config-next": "15.4.1", "@eslint/eslintrc": "^3", "cross-env": "^7.0.3", "@next/bundle-analyzer": "^15.0.0"}}
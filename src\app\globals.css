@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #171717;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

body {
  background: #fff;
  color: var(--foreground);
  font-family: var(--font-sans, sans-serif);
}

.section {
  padding-left: 2rem;
  padding-right: 2rem;
  margin-top: 2.5rem;
  margin-bottom: 2.5rem;
  background: #fff;
  color: var(--foreground);
  border-radius: 1.5rem;
  box-shadow: 0 4px 32px rgba(0,0,0,0.07);
  transition: box-shadow 0.3s, transform 0.3s;
}
.section:hover {
  box-shadow: 0 8px 40px rgba(0,0,0,0.12);
  transform: translateY(-2px) scale(1.01);
}

@media (max-width: 768px) {
  .section {
    padding-left: 1rem;
    padding-right: 1rem;
  }
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

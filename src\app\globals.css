@import "tailwindcss";

/* Modern CSS Variables */
:root {
  /* Colors */
  --white: #ffffff;
  --black: #000000;
  --gray-50: #fafafa;
  --gray-100: #f4f4f5;
  --gray-200: #e4e4e7;
  --gray-300: #d4d4d8;
  --gray-400: #a1a1aa;
  --gray-500: #71717a;
  --gray-600: #52525b;
  --gray-700: #3f3f46;
  --gray-800: #27272a;
  --gray-900: #18181b;
  --gray-950: #09090b;

  /* Orange Accent */
  --orange-50: #fff7ed;
  --orange-100: #ffedd5;
  --orange-200: #fed7aa;
  --orange-300: #fdba74;
  --orange-400: #fb923c;
  --orange-500: #f97316;
  --orange-600: #ea580c;
  --orange-700: #c2410c;
  --orange-800: #9a3412;
  --orange-900: #7c2d12;

  /* Semantic Colors */
  --background: var(--white);
  --foreground: var(--gray-950);
  --muted: var(--gray-50);
  --muted-foreground: var(--gray-500);
  --border: var(--gray-200);
  --accent: var(--orange-500);
  --accent-foreground: var(--white);

  /* Spacing */
  --container-padding: clamp(1rem, 5vw, 3rem);
  --section-padding: clamp(3rem, 8vw, 6rem);

  /* Typography */
  --font-sans: var(--font-geist-sans, system-ui, sans-serif);
  --font-mono: var(--font-geist-mono, 'Fira Code', monospace);

  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);

  /* Border Radius */
  --radius-sm: 0.375rem;
  --radius: 0.5rem;
  --radius-md: 0.75rem;
  --radius-lg: 1rem;
  --radius-xl: 1.5rem;
  --radius-2xl: 2rem;
  --radius-full: 9999px;
}

/* Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
  font-size: 16px;
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-sans);
  line-height: 1.6;
  overflow-x: hidden;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Modern Container */
.container {
  width: 100%;
  max-width: 1400px;
  margin: 0 auto;
  padding-left: var(--container-padding);
  padding-right: var(--container-padding);
}

/* Modern Section Layouts */
.section-full {
  width: 100%;
  position: relative;
}

.section-white {
  background: var(--white);
  color: var(--foreground);
}

.section-muted {
  background: var(--muted);
  color: var(--foreground);
}

.section-dark {
  background: var(--gray-900);
  color: var(--white);
}

.section-black {
  background: var(--black);
  color: var(--white);
}

/* Modern Typography */
.text-gradient {
  background: linear-gradient(135deg, var(--accent), var(--orange-600));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Modern Buttons */
.btn-primary {
  background: var(--accent);
  color: var(--accent-foreground);
  border: none;
  border-radius: var(--radius-full);
  padding: 0.875rem 2rem;
  font-weight: 600;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: var(--shadow);
}

.btn-primary:hover {
  background: var(--orange-600);
  box-shadow: var(--shadow-lg);
  transform: translateY(-1px);
}

.btn-secondary {
  background: transparent;
  color: var(--foreground);
  border: 2px solid var(--border);
  border-radius: var(--radius-full);
  padding: 0.875rem 2rem;
  font-weight: 600;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn-secondary:hover {
  background: var(--foreground);
  color: var(--background);
  border-color: var(--foreground);
}

/* Modern Cards */
.card {
  background: var(--background);
  border: 1px solid var(--border);
  border-radius: var(--radius-xl);
  padding: 2rem;
  box-shadow: var(--shadow-sm);
  transition: all 0.3s ease;
}

.card:hover {
  box-shadow: var(--shadow-xl);
  transform: translateY(-4px);
}

/* Modern Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.animate-fade-in-up {
  animation: fadeInUp 0.6s ease-out;
}

.animate-fade-in-left {
  animation: fadeInLeft 0.6s ease-out;
}

.animate-fade-in-right {
  animation: fadeInRight 0.6s ease-out;
}

/* Modern Scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--muted);
}

::-webkit-scrollbar-thumb {
  background: var(--accent);
  border-radius: var(--radius-full);
}

::-webkit-scrollbar-thumb:hover {
  background: var(--orange-600);
}

/* Modern Focus States */
*:focus-visible {
  outline: 2px solid var(--accent);
  outline-offset: 2px;
}

/* Modern Responsive Design */
@media (max-width: 640px) {
  html {
    font-size: 14px;
  }

  .container {
    padding-left: 1rem;
    padding-right: 1rem;
  }
}

@media (min-width: 1920px) {
  .container {
    max-width: 1600px;
  }
}

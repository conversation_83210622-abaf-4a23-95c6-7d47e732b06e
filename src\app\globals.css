@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #000000;
  --accent: #ff6b35;
  --accent-light: #ff8c5a;
  --accent-dark: #e55a2b;
  --gray-50: #f9fafb;
  --gray-100: #f3f4f6;
  --gray-200: #e5e7eb;
  --gray-300: #d1d5db;
  --gray-400: #9ca3af;
  --gray-500: #6b7280;
  --gray-600: #4b5563;
  --gray-700: #374151;
  --gray-800: #1f2937;
  --gray-900: #111827;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-accent: var(--accent);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-sans, sans-serif);
  line-height: 1.6;
  overflow-x: hidden;
}

/* Container for content with proper spacing */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

@media (min-width: 640px) {
  .container {
    padding: 0 2rem;
  }
}

@media (min-width: 1024px) {
  .container {
    padding: 0 3rem;
  }
}

/* Full width sections */
.section-full {
  width: 100%;
  position: relative;
}

/* Section with alternating backgrounds */
.section-white {
  background: #ffffff;
  color: #000000;
}

.section-gray {
  background: var(--gray-50);
  color: #000000;
}

.section-dark {
  background: var(--gray-900);
  color: #ffffff;
}

.section-black {
  background: #000000;
  color: #ffffff;
}

/* Accent colors */
.text-accent {
  color: var(--accent);
}

.bg-accent {
  background-color: var(--accent);
}

.border-accent {
  border-color: var(--accent);
}

/* Smooth transitions */
.transition-all {
  transition: all 0.3s ease;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--gray-100);
}

::-webkit-scrollbar-thumb {
  background: var(--accent);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--accent-dark);
}

import React from 'react'

const Services = () => {
  const services = [
    {
      icon: '🎨',
      title: 'Web Design',
      description: 'Beautiful and functional web designs that convert visitors into customers.'
    },
    {
      icon: '💻',
      title: 'Development',
      description: 'Custom web development solutions using modern technologies and frameworks.'
    },
    {
      icon: '📱',
      title: 'Mobile Apps',
      description: 'Native and cross-platform mobile applications for iOS and Android.'
    },
    {
      icon: '🚀',
      title: 'Digital Marketing',
      description: 'Strategic digital marketing campaigns to grow your online presence.'
    }
  ]

  return (
    <section id="services" className="py-20 bg-white">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <h2 className="text-4xl font-bold text-black mb-4">
            Our <span className="text-orange-500">Services</span>
          </h2>
          <p className="text-gray-600 max-w-2xl mx-auto">
            We offer a comprehensive range of digital services to help your business succeed online.
          </p>
        </div>

        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
          {services.map((service, index) => (
            <div key={index} className="bg-gray-50 p-8 rounded-xl hover:shadow-lg transition-shadow group">
              <div className="text-4xl mb-4 group-hover:scale-110 transition-transform">
                {service.icon}
              </div>
              <h3 className="text-xl font-bold text-black mb-3">{service.title}</h3>
              <p className="text-gray-600">{service.description}</p>
              <div className="mt-4">
                <span className="text-orange-500 font-semibold hover:underline cursor-pointer">
                  Learn More →
                </span>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  )
}

export default Services
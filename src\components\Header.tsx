'use client'
import React, { useState, useEffect } from 'react'
import Link from 'next/link'
import { Menu, X, BookOpen, User, Search } from 'lucide-react'

const Header = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false)
  const [isScrolled, setIsScrolled] = useState(false)

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 50)
    }
    window.addEventListener('scroll', handleScroll)
    return () => window.removeEventListener('scroll', handleScroll)
  }, [])

  const navItems = [
    { href: '#home', label: 'Home' },
    { href: '#courses', label: 'Courses' },
    { href: '#about', label: 'About' },
    { href: '#instructors', label: 'Instructors' },
    { href: '#testimonials', label: 'Reviews' },
    { href: '#contact', label: 'Contact' },
  ]

  return (
    <header
      className={`fixed top-0 w-full z-50 transition-all duration-300 ${
        isScrolled
          ? 'bg-white/95 backdrop-blur-md shadow-lg border-b border-gray-200'
          : 'bg-white shadow-md'
      }`}
    >
      <div className="w-full">
        <div className="container mx-auto flex items-center justify-between h-16 lg:h-20">
          {/* Logo */}
          <div>
            <Link
              href="/"
              className="flex items-center space-x-2 text-2xl lg:text-3xl font-bold text-black hover:text-gray-700 transition-colors duration-200"
            >
              <BookOpen className="w-8 h-8 lg:w-10 lg:h-10 text-orange-500" />
              <span>EduTech<span className="text-orange-500">.</span></span>
            </Link>
          </div>

          {/* Desktop Navigation */}
          <nav className="hidden lg:flex space-x-8 xl:space-x-12">
            {navItems.map((item) => (
              <div key={item.href}>
                <Link
                  href={item.href}
                  className="relative text-gray-700 hover:text-black transition-colors duration-200 group font-medium text-lg"
                >
                  {item.label}
                  <span className="absolute -bottom-1 left-0 w-0 h-0.5 bg-orange-500 transition-all duration-300 group-hover:w-full"></span>
                </Link>
              </div>
            ))}
          </nav>

          {/* Search & CTA */}
          <div className="hidden lg:flex items-center space-x-4">
            <button
              className="p-2 text-gray-600 hover:text-orange-500 transition-colors duration-200"
            >
              <Search className="w-5 h-5" />
            </button>
            <button
              className="p-2 text-gray-600 hover:text-orange-500 transition-colors duration-200"
            >
              <User className="w-5 h-5" />
            </button>
            <div>
              <Link
                href="#courses"
                className="bg-orange-500 hover:bg-orange-600 text-white px-6 py-3 rounded-full font-medium hover:shadow-lg transition-all duration-200 text-sm lg:text-base"
              >
                Start Learning
              </Link>
            </div>
          </div>

          {/* Mobile Menu Button */}
          <button
            className="lg:hidden text-gray-700 hover:text-orange-500 transition-colors duration-200 p-2"
            onClick={() => setIsMenuOpen(!isMenuOpen)}
            aria-label="Toggle menu"
          >
            {isMenuOpen ? <X size={24} /> : <Menu size={24} />}
          </button>
        </div>

        {/* Mobile Menu */}
        {isMenuOpen && (
          <div className="lg:hidden overflow-hidden">
            <nav className="bg-white shadow-lg border-t border-gray-200 py-4">
              {navItems.map((item) => (
                <div key={item.href}>
                  <Link
                    href={item.href}
                    className="block px-6 py-3 text-gray-700 hover:text-black hover:bg-orange-50 transition-all duration-200 font-medium"
                    onClick={() => setIsMenuOpen(false)}
                  >
                    {item.label}
                  </Link>
                </div>
              ))}
              <div className="px-6 pt-4 space-y-3">
                <div className="flex items-center space-x-4 mb-4">
                  <button className="p-2 text-gray-600 hover:text-orange-500 transition-colors duration-200">
                    <Search className="w-5 h-5" />
                  </button>
                  <button className="p-2 text-gray-600 hover:text-orange-500 transition-colors duration-200">
                    <User className="w-5 h-5" />
                  </button>
                </div>
                <Link
                  href="#courses"
                  className="block w-full text-center bg-orange-500 hover:bg-orange-600 text-white py-3 rounded-full font-medium transition-all duration-200"
                  onClick={() => setIsMenuOpen(false)}
                >
                  Start Learning
                </Link>
              </div>
            </nav>
          </div>
        )}
      </div>
    </header>
  )
}

export default Header


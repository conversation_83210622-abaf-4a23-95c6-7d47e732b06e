'use client'
import React, { useState, useEffect } from 'react'
import Link from 'next/link'
import { Menu, X, BookOpen, User, Search } from 'lucide-react'
// Removed framer-motion for stability

const Header = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false)
  const [isScrolled, setIsScrolled] = useState(false)

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 50)
    }
    window.addEventListener('scroll', handleScroll)
    return () => window.removeEventListener('scroll', handleScroll)
  }, [])

  const navItems = [
    { href: '#home', label: 'Home' },
    { href: '#courses', label: 'Courses' },
    { href: '#about', label: 'About' },
    { href: '#instructors', label: 'Instructors' },
    { href: '#testimonials', label: 'Reviews' },
    { href: '#contact', label: 'Contact' },
  ]

  return (
    <header 
      className="fixed top-0 w-full z-50 transition-all duration-300 bg-white shadow-md"
    >
      <div className="w-full">
        <div className="max-w-screen-xl mx-auto px-4 md:px-8 flex items-center justify-between h-16 flex-wrap">
          {/* Logo */}
          <div>
            <Link 
              href="/" 
              className="flex items-center space-x-2 text-2xl font-bold text-blue-600"
            >
              <BookOpen className="w-8 h-8" />
              <span>EduTech<span className="text-orange-500">.</span></span>
            </Link>
          </div>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex space-x-8">
            {navItems.map((item) => (
              <div key={item.href}>
                <Link
                  href={item.href}
                  className="relative text-gray-700 hover:text-blue-600 transition-colors duration-200 group font-medium"
                >
                  {item.label}
                  <span className="absolute -bottom-1 left-0 w-0 h-0.5 bg-blue-600 transition-all duration-300 group-hover:w-full"></span>
                </Link>
              </div>
            ))}
          </nav>

          {/* Search & CTA */}
          <div className="hidden md:flex items-center space-x-4">
            <button
              className="p-2 text-gray-600 hover:text-blue-600 transition-colors"
            >
              <Search className="w-5 h-5" />
            </button>
            <button
              className="p-2 text-gray-600 hover:text-blue-600 transition-colors"
            >
              <User className="w-5 h-5" />
            </button>
            <div>
              <Link
                href="#courses"
                className="bg-gradient-to-r from-blue-600 to-purple-600 text-white px-6 py-2 rounded-full hover:shadow-lg transition-all duration-200"
              >
                Start Learning
              </Link>
            </div>
          </div>

          {/* Mobile Menu Button */}
          <button
            className="md:hidden text-gray-700 hover:text-blue-600 transition-colors duration-200"
            onClick={() => setIsMenuOpen(!isMenuOpen)}
            aria-label="Toggle menu"
          >
            {isMenuOpen ? <X size={24} /> : <Menu size={24} />}
          </button>
        </div>

        {/* Mobile Menu */}
        {isMenuOpen && (
          <div className="md:hidden overflow-hidden">
            <nav className="bg-white shadow-md border-t py-4">
              {navItems.map((item) => (
                <div key={item.href}>
                  <Link
                    href={item.href}
                    className="block px-4 py-3 text-gray-700 hover:text-blue-600 hover:bg-blue-50 transition-all duration-200 font-medium"
                    onClick={() => setIsMenuOpen(false)}
                  >
                    {item.label}
                  </Link>
                </div>
              ))}
              <div className="px-4 pt-4 space-y-3">
                <Link
                  href="#courses"
                  className="block w-full text-center bg-gradient-to-r from-blue-600 to-purple-600 text-white py-3 rounded-full transition-all duration-200"
                  onClick={() => setIsMenuOpen(false)}
                >
                  Start Learning
                </Link>
              </div>
            </nav>
          </div>
        )}
      </div>
    </header>
  )
}

export default Header


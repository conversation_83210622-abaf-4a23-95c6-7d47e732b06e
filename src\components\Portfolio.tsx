'use client'
import React, { useState } from 'react'

const Portfolio = () => {
  const [activeFilter, setActiveFilter] = useState('all')

  const projects = [
    {
      id: 1,
      title: 'E-commerce Platform',
      category: 'web',
      image: '🛒',
      description: 'Modern e-commerce solution with advanced features'
    },
    {
      id: 2,
      title: 'Mobile Banking App',
      category: 'mobile',
      image: '💳',
      description: 'Secure and user-friendly banking application'
    },
    {
      id: 3,
      title: 'Brand Identity',
      category: 'design',
      image: '🎨',
      description: 'Complete brand identity and logo design'
    },
    {
      id: 4,
      title: 'Restaurant Website',
      category: 'web',
      image: '🍕',
      description: 'Beautiful restaurant website with online ordering'
    },
    {
      id: 5,
      title: 'Fitness App',
      category: 'mobile',
      image: '💪',
      description: 'Comprehensive fitness tracking application'
    },
    {
      id: 6,
      title: 'Corporate Branding',
      category: 'design',
      image: '🏢',
      description: 'Professional corporate identity design'
    }
  ]

  const filters = ['all', 'web', 'mobile', 'design']

  const filteredProjects = activeFilter === 'all' 
    ? projects 
    : projects.filter(project => project.category === activeFilter)

  return (
    <section id="portfolio" className="py-20 bg-gray-50">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <h2 className="text-4xl font-bold text-black mb-4">
            Our <span className="text-orange-500">Portfolio</span>
          </h2>
          <p className="text-gray-600 max-w-2xl mx-auto mb-8">
            Take a look at some of our recent projects and see how we've helped businesses achieve their goals.
          </p>

          {/* Filter Buttons */}
          <div className="flex justify-center space-x-4 mb-12">
            {filters.map((filter) => (
              <button
                key={filter}
                onClick={() => setActiveFilter(filter)}
                className={`px-6 py-2 rounded-full capitalize transition-colors ${
                  activeFilter === filter
                    ? 'bg-orange-500 text-white'
                    : 'bg-white text-black hover:bg-orange-100'
                }`}
              >
                {filter}
              </button>
            ))}
          </div>
        </div>

        {/* Portfolio Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          {filteredProjects.map((project) => (
            <div key={project.id} className="bg-white rounded-xl overflow-hidden shadow-lg hover:shadow-xl transition-shadow group">
              <div className="h-48 bg-gradient-to-br from-gray-100 to-gray-200 flex items-center justify-center text-6xl group-hover:scale-105 transition-transform">
                {project.image}
              </div>
              <div className="p-6">
                <h3 className="text-xl font-bold text-black mb-2">{project.title}</h3>
                <p className="text-gray-600 mb-4">{project.description}</p>
                <span className="inline-block px-3 py-1 bg-orange-100 text-orange-500 text-sm rounded-full">
                  {project.category}
                </span>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  )
}

export default Portfolio